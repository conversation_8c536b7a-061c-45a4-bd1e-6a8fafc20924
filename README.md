**数据输入格式：**
- 读取Excel文件，包含三列：第1列=问题，第2列=标准答案，第3列=知识库ID

**API集成要求：**
- 现有的RAG API调用，改为调用FastGPT的知识库搜索接口：`https://api.fastgpt.in/api/core/dataset/searchTest`
- 使用知识库ID作为查询参数，获取多条相关文档数据
请求示例：
curl --location --request POST 'https://api.fastgpt.in/api/core/dataset/searchTest' \
--header 'Authorization: Bearer fastgpt-xxxxx' \
--header 'Content-Type: application/json' \
--data-raw '{
    "datasetId": "知识库的ID",
    "text": "导演是谁",
    "limit": 5000,
    "similarity": 0,
    "searchMode": "embedding",
    "usingReRank": false,

    "datasetSearchUsingExtensionQuery": true,
    "datasetSearchExtensionModel": "gpt-4o-mini",
    "datasetSearchExtensionBg": ""
}'

参数说明：
    datasetId - 知识库ID
    text - 需要测试的文本
    limit - 最大 tokens 数量
    similarity - 最低相关度（0~1，可选）
    searchMode - 搜索模式：embedding | fullTextRecall | mixedRecall
    usingReRank - 使用重排
    datasetSearchUsingExtensionQuery - 使用问题优化
    datasetSearchExtensionModel - 问题优化模型
    datasetSearchExtensionBg - 问题优化背景描述
返回示例：
{
  "code": 200,
  "statusText": "",
  "data": [
    {
        "id": "65599c54a5c814fb803363cb",
        "q": "你是谁",
        "a": "我是FastGPT助手",
        "datasetId": "6554684f7f9ed18a39a4d15c",
        "collectionId": "6556cd795e4b663e770bb66d",
        "sourceName": "GBT 15104-2021 装饰单板贴面人造板.pdf",
        "sourceId": "6556cd775e4b663e770bb65c",
        "score": 0.8050316572189331
    },
    ......
  ]
}

deepseek的apikey为  ***********************************
fastgpt接口密钥为 fastgpt-kWwFdITMrh4ADnaGWPzqckVi3Xg5O6AC1u5Y8S3wkW2einZZOjUXMDwI

具体要求：我需要使用deepeval库结合deepseek模型分析，进行知识库召回率测试。
步骤：
1、读取FastGPT.xlsx文件，包含三列：第1列=问题，第2列=标准答案，第3列=知识库ID，跳过三列中其中有一个为空或者为 不明 的数据
2、根据问题和知识库id查询searchTest接口获取RAG返回的多条数据
3、deepeval和deepseek进行召回率分析
4、结果保存到新Excel里面（包含问题、答案、知识库ID、召回率、上下文数量、最高上下文分数、平均上下文分数、上下文1、分数1、上下文2、分数2、上下文3、分数3、上下文4、分数4、上下文5、分数5）
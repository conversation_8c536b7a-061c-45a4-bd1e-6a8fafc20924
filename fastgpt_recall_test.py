#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastGPT知识库召回率测试脚本
使用deepeval库结合deepseek模型进行召回率分析
"""

import pandas as pd
import requests
import json
import time
from typing import List, Dict, Any, Optional
import logging
from deepeval import evaluate
from deepeval.metrics import ContextualRecallMetric
from deepeval.test_case import LLMTestCase
from deepeval.models import DeepEvalBaseLLM

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API配置
FASTGPT_API_URL = "http://171.43.138.237:3000/api/core/dataset/searchTest"
FASTGPT_API_KEY = "fastgpt-kWwFdITMrh4ADnaGWPzqckVi3Xg5O6AC1u5Y8S3wkW2einZZOjUXMDwI"
DEEPSEEK_API_KEY = "***********************************"

class DeepSeekModel(DeepEvalBaseLLM):
    """DeepSeek模型包装器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.model_name = "deepseek-chat"
        
    def load_model(self):
        return self
        
    def generate(self, prompt: str) -> str:
        """调用DeepSeek API生成响应"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1
        }
        
        try:
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return ""
    
    async def a_generate(self, prompt: str) -> str:
        return self.generate(prompt)
    
    def get_model_name(self) -> str:
        return self.model_name

def read_excel_data(file_path: str) -> pd.DataFrame:
    """读取Excel文件并过滤数据"""
    try:
        df = pd.read_excel(file_path)
        logger.info(f"成功读取Excel文件，共{len(df)}行数据")
        
        # 假设列名为：问题、标准答案、知识库ID
        if len(df.columns) >= 3:
            df.columns = ['问题', '标准答案', '知识库ID'] + list(df.columns[3:])
        
        # 过滤掉空值或"不明"的数据
        original_count = len(df)
        df = df.dropna(subset=['问题', '标准答案', '知识库ID'])
        df = df[~df['问题'].isin(['不明', ''])]
        df = df[~df['标准答案'].isin(['不明', ''])]
        df = df[~df['知识库ID'].isin(['不明', ''])]
        
        filtered_count = len(df)
        logger.info(f"过滤后剩余{filtered_count}行数据，过滤掉{original_count - filtered_count}行")
        
        return df
    except Exception as e:
        logger.error(f"读取Excel文件失败: {e}")
        raise

def search_fastgpt_knowledge(question: str, dataset_id: str) -> List[Dict[str, Any]]:
    """调用FastGPT知识库搜索接口"""
    headers = {
        "Authorization": f"Bearer {FASTGPT_API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "datasetId": dataset_id,
        "text": question,
        "limit": 5000,
        "similarity": 0,
        "searchMode": "mixedRecall",
        "usingReRank": False,
        "datasetSearchUsingExtensionQuery": True,
        "datasetSearchExtensionModel": "qwen-plus-2025-04-28",
        "datasetSearchExtensionBg": ""
    }
    
    try:
        response = requests.post(FASTGPT_API_URL, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        
        if result.get("code") == 200:
            return result.get("data", [])
        else:
            logger.error(f"FastGPT API返回错误: {result}")
            return []
    except Exception as e:
        logger.error(f"FastGPT API调用失败: {e}")
        return []

def generate_answer_from_contexts(question: str, contexts: List[str], model: DeepSeekModel) -> str:
    """基于检索到的上下文生成回答"""
    if not contexts:
        return "无相关内容"

    # 构建提示词
    context_text = "\n".join([f"上下文{i+1}: {ctx}" for i, ctx in enumerate(contexts[:3])])
    prompt = f"""基于以下上下文信息回答问题：

{context_text}

问题：{question}

请基于上述上下文信息简洁地回答问题："""

    try:
        return model.generate(prompt)
    except Exception as e:
        logger.error(f"生成回答失败: {e}")
        return "生成回答失败"

def calculate_recall_with_deepeval(question: str, expected_answer: str, contexts: List[str], model: DeepSeekModel) -> float:
    """使用deepeval计算召回率"""
    try:
        # 使用模型基于上下文生成实际输出
        actual_output = generate_answer_from_contexts(question, contexts, model)

        # 创建测试用例
        test_case = LLMTestCase(
            input=question,
            actual_output=actual_output,
            expected_output=expected_answer,
            retrieval_context=contexts
        )

        # 创建召回率指标
        recall_metric = ContextualRecallMetric(model=model, threshold=0.5)

        # 计算召回率
        recall_metric.measure(test_case)
        return recall_metric.score
    except Exception as e:
        logger.error(f"召回率计算失败: {e}")
        return 0.0

def process_single_row(row: pd.Series, model: DeepSeekModel) -> Dict[str, Any]:
    """处理单行数据"""
    question = row['问题']
    expected_answer = row['标准答案']
    dataset_id = row['知识库ID']
    
    logger.info(f"处理问题: {question[:50]}...")
    
    # 调用FastGPT搜索接口
    search_results = search_fastgpt_knowledge(question, dataset_id)
    search_results = search_results["list"]
    # 提取上下文和分数
    contexts = []
    scores = []
    for result in search_results[:5]:  # 最多取前5个结果
        contexts.append(result.get('q', ''))
        for i in result['score']:
            if i['type'] == "rrf":
                scores.append(i['value'])
    
    # 计算召回率
    recall_score = calculate_recall_with_deepeval(question, expected_answer, contexts, model)
    
    # 计算统计信息
    context_count = len(contexts)
    max_score = max(scores) if scores else 0
    avg_score = sum(scores) / len(scores) if scores else 0
    
    # 构建结果
    result = {
        '问题': question,
        '答案': expected_answer,
        '知识库ID': dataset_id,
        '召回率': recall_score,
        '上下文数量': context_count,
        '最高上下文分数': max_score,
        '平均上下文分数': avg_score
    }
    
    # 添加前5个上下文和分数
    for i in range(5):
        if i < len(contexts):
            result[f'上下文{i+1}'] = contexts[i]
            result[f'分数{i+1}'] = scores[i]
        else:
            result[f'上下文{i+1}'] = ''
            result[f'分数{i+1}'] = 0
    
    return result

def main():
    """主函数"""
    try:
        # 初始化DeepSeek模型
        logger.info("初始化DeepSeek模型...")
        model = DeepSeekModel(DEEPSEEK_API_KEY)
        
        # 读取Excel数据
        logger.info("读取Excel数据...")
        df = read_excel_data("FastGPTdemo.xlsx")
        
        if df.empty:
            logger.warning("没有有效数据需要处理")
            return
        
        # 处理每行数据
        results = []
        total_rows = len(df)
        
        for index, row in df.iterrows():
            logger.info(f"处理进度: {index + 1}/{total_rows}")
            try:
                result = process_single_row(row, model)
                results.append(result)
                
                # 添加延时避免API限制
                time.sleep(1)
            except Exception as e:
                logger.error(f"处理第{index + 1}行数据失败: {e}")
                continue
        
        # 保存结果到Excel
        if results:
            logger.info("保存结果到Excel文件...")
            results_df = pd.DataFrame(results)
            output_file = "fastgpt_recall_results.xlsx"
            results_df.to_excel(output_file, index=False)
            logger.info(f"结果已保存到: {output_file}")
            
            # 打印统计信息
            avg_recall = results_df['召回率'].mean()
            logger.info(f"平均召回率: {avg_recall:.4f}")
            logger.info(f"处理完成，共处理{len(results)}条数据")
        else:
            logger.warning("没有成功处理的数据")
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看结果文件内容
"""

import pandas as pd

# 读取结果文件
df = pd.read_excel("fastgpt_recall_results.xlsx")

# 显示所有列
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("结果文件内容：")
print("=" * 80)
print(df.to_string(index=False))

print("\n" + "=" * 80)
print("列名：")
for i, col in enumerate(df.columns):
    print(f"{i+1}. {col}")

print(f"\n总行数: {len(df)}")
print(f"平均召回率: {df['召回率'].mean():.4f}")

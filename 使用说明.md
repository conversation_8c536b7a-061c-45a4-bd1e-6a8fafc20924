# FastGPT知识库召回率测试脚本使用说明

## 功能描述
本脚本用于测试FastGPT知识库的召回率，使用deepeval库结合deepseek模型进行分析。

## 环境要求
- Python 3.8+
- 网络连接（需要访问FastGPT和DeepSeek API）

## 安装依赖
```bash
pip install -r requirements.txt
```

## 输入文件要求
- 文件名：`FastGPT.xlsx`
- 文件格式：Excel文件，包含三列
  - 第1列：问题
  - 第2列：标准答案  
  - 第3列：知识库ID
- 注意：脚本会自动跳过三列中任意一列为空或为"不明"的数据

## 运行脚本
```bash
python fastgpt_recall_test.py
```

## 输出结果
脚本会生成一个名为 `fastgpt_recall_results.xlsx` 的Excel文件，包含以下列：
- 问题
- 答案
- 知识库ID
- 召回率
- 上下文数量
- 最高上下文分数
- 平均上下文分数
- 上下文1~5（最多5个上下文）
- 分数1~5（对应上下文的相关度分数）

## API配置
脚本中已配置好以下API密钥：
- FastGPT API Key: `fastgpt-kWwFdITMrh4ADnaGWPzqckVi3Xg5O6AC1u5Y8S3wkW2einZZOjUXMDwI`
- DeepSeek API Key: `***********************************`

## 注意事项
1. 脚本会在每次API调用之间添加1秒延时，避免触发API限制
2. 如果某行数据处理失败，脚本会记录错误并继续处理下一行
3. 处理过程中会显示进度信息和日志
4. 最终会显示平均召回率统计信息

## 故障排除
1. 如果遇到网络错误，请检查网络连接
2. 如果API调用失败，请检查API密钥是否正确
3. 如果Excel文件读取失败，请检查文件格式和路径
4. 查看控制台日志获取详细错误信息
